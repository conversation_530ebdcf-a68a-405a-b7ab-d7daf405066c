#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل خدمة Python Screenshot Service للنظام v4.0
يجب تشغيل هذا الملف قبل استخدام النظام لضمان عمل الصور
"""

import os
import sys
import subprocess
import time
import requests
import threading
from pathlib import Path

def check_service_running(port=8000):
    """التحقق من تشغيل الخدمة"""
    try:
        response = requests.get(f'http://localhost:{port}/health', timeout=3)
        return response.status_code == 200
    except:
        return False

def install_requirements():
    """تثبيت المتطلبات المطلوبة"""
    print("🔧 تثبيت المتطلبات المطلوبة...")
    
    requirements = [
        'flask',
        'selenium',
        'webdriver-manager',
        'pillow',
        'playwright',
        'flask-cors'
    ]
    
    for req in requirements:
        try:
            print(f"📦 تثبيت {req}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', req])
            print(f"✅ تم تثبيت {req} بنجاح")
        except subprocess.CalledProcessError as e:
            print(f"❌ فشل في تثبيت {req}: {e}")
            return False
    
    return True

def install_playwright_browsers():
    """تثبيت متصفحات Playwright"""
    try:
        print("🌐 تثبيت متصفحات Playwright...")
        subprocess.check_call([sys.executable, '-m', 'playwright', 'install'])
        print("✅ تم تثبيت متصفحات Playwright بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت متصفحات Playwright: {e}")
        return False

def start_service():
    """تشغيل خدمة Python Screenshot Service"""
    service_path = Path(__file__).parent / 'assets' / 'modules' / 'bugbounty' / 'python_web_service.py'
    
    if not service_path.exists():
        print(f"❌ لم يتم العثور على ملف الخدمة: {service_path}")
        return False
    
    print(f"🚀 تشغيل Python Screenshot Service من: {service_path}")
    
    try:
        # تشغيل الخدمة في خيط منفصل
        process = subprocess.Popen([
            sys.executable, str(service_path)
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        print("⏳ انتظار تشغيل الخدمة...")
        time.sleep(5)
        
        # التحقق من تشغيل الخدمة
        if check_service_running():
            print("✅ Python Screenshot Service تعمل بنجاح على المنفذ 8000")
            print("🌐 يمكن الوصول للخدمة على: http://localhost:8000")
            return True
        else:
            print("❌ فشل في تشغيل Python Screenshot Service")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخدمة: {e}")
        return False

def create_screenshots_directory():
    """إنشاء مجلد الصور"""
    screenshots_dir = Path(__file__).parent / 'screenshots'
    screenshots_dir.mkdir(exist_ok=True)
    print(f"📁 تم إنشاء مجلد الصور: {screenshots_dir}")

def main():
    """الدالة الرئيسية"""
    print("🐍 Python Screenshot Service Starter v4.0")
    print("=" * 50)
    
    # التحقق من تشغيل الخدمة مسبقاً
    if check_service_running():
        print("✅ Python Screenshot Service تعمل بالفعل")
        return
    
    # إنشاء مجلد الصور
    create_screenshots_directory()
    
    # تثبيت المتطلبات
    print("🔧 التحقق من المتطلبات...")
    if not install_requirements():
        print("❌ فشل في تثبيت المتطلبات")
        return
    
    # تثبيت متصفحات Playwright
    if not install_playwright_browsers():
        print("⚠️ تحذير: فشل في تثبيت متصفحات Playwright، سيتم استخدام Selenium فقط")
    
    # تشغيل الخدمة
    if start_service():
        print("\n🎉 تم تشغيل Python Screenshot Service بنجاح!")
        print("📋 معلومات الخدمة:")
        print("   - المنفذ: 8000")
        print("   - العنوان: http://localhost:8000")
        print("   - مجلد الصور: ./screenshots/")
        print("\n💡 يمكنك الآن استخدام نظام Bug Bounty v4.0 مع الصور الحقيقية")
        print("🔄 لإيقاف الخدمة، اضغط Ctrl+C")
        
        try:
            # إبقاء البرنامج يعمل
            while True:
                time.sleep(10)
                if not check_service_running():
                    print("⚠️ الخدمة توقفت، محاولة إعادة التشغيل...")
                    start_service()
        except KeyboardInterrupt:
            print("\n🛑 تم إيقاف Python Screenshot Service")
    else:
        print("❌ فشل في تشغيل Python Screenshot Service")
        print("\n🔧 حلول مقترحة:")
        print("1. تأكد من تثبيت Python 3.7+ بشكل صحيح")
        print("2. تأكد من وجود pip وإمكانية تثبيت الحزم")
        print("3. تأكد من عدم استخدام المنفذ 8000 من برنامج آخر")
        print("4. جرب تشغيل الأمر كمدير (Administrator)")

if __name__ == "__main__":
    main()
