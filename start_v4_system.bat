@echo off
chcp 65001 >nul
echo ========================================
echo    🚀 Bug Bounty v4.0 System Startup
echo ========================================
echo.

echo [1/3] 🔧 Checking Python installation...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python not found! Please install Python 3.7+
    pause
    exit /b 1
)

echo.
echo [2/3] 🐍 Starting Python Screenshot Service...
start "Python Screenshot Service" cmd /k "python start_python_service.py"

echo Waiting for Python service to start...
timeout /t 5 /nobreak > nul

echo.
echo [3/3] 🌐 Starting Bug Bounty v4.0 System...
echo Opening index.html in default browser...
start index.html

echo.
echo ========================================
echo   ✅ Bug Bounty v4.0 System Started!
echo ========================================
echo.
echo 🐍 Python Service: http://localhost:8000/health
echo 🌐 Main System: index.html
echo 📸 Screenshots: ./screenshots/ folder
echo.
echo 📋 Quick Test Commands:
echo   - Open browser console and run: bugBountyCore.pythonBridge.checkServiceStatus()
echo   - Test screenshot: bugBountyCore.captureWebsiteScreenshotV4('https://example.com', 'test')
echo.
echo Press any key to exit...
pause > nul
